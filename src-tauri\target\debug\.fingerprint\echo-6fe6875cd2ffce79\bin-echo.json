{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[]", "target": 12538796586915257791, "profile": 17672942494452627365, "path": 4942398508502643691, "deps": [[550086679149313511, "global_hotkey", false, 5866405930179204582], [5986029879202738730, "log", false, 10558347002861123941], [6416823254013318197, "tauri_plugin_fs", false, 7800189351343479810], [7530703367363979314, "rodio", false, 17515795834108694204], [8324462083842905811, "tauri_plugin_log", false, 16195699290590498021], [9538054652646069845, "tokio", false, 9785975403020828866], [9689903380558560274, "serde", false, 5173668102337234525], [10197015723251394741, "app_lib", false, 15829688101247986820], [10197015723251394741, "build_script_build", false, 2622144224930705903], [13625485746686963219, "anyhow", false, 7176076525327837409], [14039947826026167952, "tauri", false, 2505739315486164683], [14525517306681678134, "tauri_plugin_dialog", false, 5983167434034286886], [15367738274754116744, "serde_json", false, 7309637273554766362], [15546644797649172673, "symphonia", false, 14950224023451892028]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\echo-6fe6875cd2ffce79\\dep-bin-echo", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}