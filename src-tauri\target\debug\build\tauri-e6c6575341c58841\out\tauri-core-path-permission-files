["\\\\?\\Z:\\musicplayer\\src-tauri\\target\\debug\\build\\tauri-e6c6575341c58841\\out\\permissions\\path\\autogenerated\\commands\\basename.toml", "\\\\?\\Z:\\musicplayer\\src-tauri\\target\\debug\\build\\tauri-e6c6575341c58841\\out\\permissions\\path\\autogenerated\\commands\\dirname.toml", "\\\\?\\Z:\\musicplayer\\src-tauri\\target\\debug\\build\\tauri-e6c6575341c58841\\out\\permissions\\path\\autogenerated\\commands\\extname.toml", "\\\\?\\Z:\\musicplayer\\src-tauri\\target\\debug\\build\\tauri-e6c6575341c58841\\out\\permissions\\path\\autogenerated\\commands\\is_absolute.toml", "\\\\?\\Z:\\musicplayer\\src-tauri\\target\\debug\\build\\tauri-e6c6575341c58841\\out\\permissions\\path\\autogenerated\\commands\\join.toml", "\\\\?\\Z:\\musicplayer\\src-tauri\\target\\debug\\build\\tauri-e6c6575341c58841\\out\\permissions\\path\\autogenerated\\commands\\normalize.toml", "\\\\?\\Z:\\musicplayer\\src-tauri\\target\\debug\\build\\tauri-e6c6575341c58841\\out\\permissions\\path\\autogenerated\\commands\\resolve.toml", "\\\\?\\Z:\\musicplayer\\src-tauri\\target\\debug\\build\\tauri-e6c6575341c58841\\out\\permissions\\path\\autogenerated\\commands\\resolve_directory.toml", "\\\\?\\Z:\\musicplayer\\src-tauri\\target\\debug\\build\\tauri-e6c6575341c58841\\out\\permissions\\path\\autogenerated\\default.toml"]