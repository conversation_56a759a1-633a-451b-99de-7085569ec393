{"rustc": 10895048813736897673, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"html-manipulation\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2225463790103693989, "path": 15206535318688532607, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 11622799964454363667], [3060637413840920116, "proc_macro2", false, 8319129890957314992], [3150220818285335163, "url", false, 13469117053291302290], [3191507132440681679, "serde_untagged", false, 13626912746751192526], [4899080583175475170, "semver", false, 226480500875875536], [5578504951057029730, "serde_with", false, 253958405226489682], [5986029879202738730, "log", false, 2980128530969235142], [6606131838865521726, "ctor", false, 11410305169889176729], [6913375703034175521, "schemars", false, 1282592435485428747], [7170110829644101142, "json_patch", false, 17354958663088037870], [8319709847752024821, "uuid", false, 14688885432787296528], [9010263965687315507, "http", false, 4157821809111752408], [9451456094439810778, "regex", false, 4585305480875941930], [9556762810601084293, "brotli", false, 12141357743228960236], [9689903380558560274, "serde", false, 6070878912102744926], [10806645703491011684, "thiserror", false, 8916265338513527458], [11655476559277113544, "cargo_metadata", false, 8320260617568381656], [11989259058781683633, "dunce", false, 9940621396166974783], [13625485746686963219, "anyhow", false, 1292039079741882281], [14232843520438415263, "html5ever", false, 6072768501095850120], [15088007382495681292, "kuchiki", false, 9900854727666941459], [15367738274754116744, "serde_json", false, 10067859309686628149], [15609422047640926750, "toml", false, 1631193712541391376], [15622660310229662834, "walkdir", false, 1189006774359693792], [15932120279885307830, "memchr", false, 15891093215126501800], [17146114186171651583, "infer", false, 5880226529093592193], [17155886227862585100, "glob", false, 13335192901601259105], [17186037756130803222, "phf", false, 4044665407946866062], [17990358020177143287, "quote", false, 14364564765239372762]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-f3b55f6fbf3a55ea\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}