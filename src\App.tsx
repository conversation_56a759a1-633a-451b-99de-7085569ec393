import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Play, Pause, Volume2, Folder, Music } from 'lucide-react'
import './App.css'

interface Track {
  id: string
  title: string
  artist: string
  duration: string
  file?: string
}

interface FileItem {
  name: string
  path: string
  isDirectory: boolean
  isAudio?: boolean
}

function App() {
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTrack, setCurrentTrack] = useState<Track | null>(null)
  const [volume, setVolume] = useState(75)
  const [currentTime, setCurrentTime] = useState('0:00')
  const [totalTime] = useState('0:00')
  const [progress, setProgress] = useState(0)
  const [terminalOutput, setTerminalOutput] = useState<string[]>([
    '> echo v1.0.0 - beautiful music player',
    '> type "help" for available commands',
    '> use "browse" to navigate files',
    '> try "ls" to list current directory'
  ])
  const [command, setCommand] = useState('')
  const [showCursor, setShowCursor] = useState(true)

  // File navigation state
  const [currentPath, setCurrentPath] = useState('C:\\')
  const [files, setFiles] = useState<FileItem[]>([])
  const [showFileBrowser, setShowFileBrowser] = useState(false)


  // Sample tracks
  const [playlist] = useState<Track[]>([
    { id: '1', title: 'Synthwave Dreams', artist: 'Neon Rider', duration: '3:45' },
    { id: '2', title: 'Digital Horizon', artist: 'Cyber Echo', duration: '4:12' },
    { id: '3', title: 'Terminal Blues', artist: 'Code Runner', duration: '2:58' },
    { id: '4', title: 'Binary Sunset', artist: 'Data Stream', duration: '5:23' },
  ])

  // Mock file system data
  const mockFileSystem: { [key: string]: FileItem[] } = {
    'C:\\': [
      { name: 'Users', path: 'C:\\Users', isDirectory: true },
      { name: 'Music', path: 'C:\\Music', isDirectory: true },
      { name: 'Program Files', path: 'C:\\Program Files', isDirectory: true },
    ],
    'C:\\Music': [
      { name: '..', path: 'C:\\', isDirectory: true },
      { name: 'Rock', path: 'C:\\Music\\Rock', isDirectory: true },
      { name: 'Electronic', path: 'C:\\Music\\Electronic', isDirectory: true },
      { name: 'Ambient', path: 'C:\\Music\\Ambient', isDirectory: true },
      { name: 'favorite_song.mp3', path: 'C:\\Music\\favorite_song.mp3', isDirectory: false, isAudio: true },
    ],
    'C:\\Music\\Electronic': [
      { name: '..', path: 'C:\\Music', isDirectory: true },
      { name: 'synthwave_dreams.mp3', path: 'C:\\Music\\Electronic\\synthwave_dreams.mp3', isDirectory: false, isAudio: true },
      { name: 'digital_horizon.mp3', path: 'C:\\Music\\Electronic\\digital_horizon.mp3', isDirectory: false, isAudio: true },
      { name: 'neon_nights.mp3', path: 'C:\\Music\\Electronic\\neon_nights.mp3', isDirectory: false, isAudio: true },
    ],
    'C:\\Music\\Rock': [
      { name: '..', path: 'C:\\Music', isDirectory: true },
      { name: 'classic_rock.mp3', path: 'C:\\Music\\Rock\\classic_rock.mp3', isDirectory: false, isAudio: true },
      { name: 'indie_vibes.mp3', path: 'C:\\Music\\Rock\\indie_vibes.mp3', isDirectory: false, isAudio: true },
    ],
    'C:\\Music\\Ambient': [
      { name: '..', path: 'C:\\Music', isDirectory: true },
      { name: 'peaceful_morning.mp3', path: 'C:\\Music\\Ambient\\peaceful_morning.mp3', isDirectory: false, isAudio: true },
      { name: 'rain_sounds.mp3', path: 'C:\\Music\\Ambient\\rain_sounds.mp3', isDirectory: false, isAudio: true },
    ],
  }

  // Load files for current path
  useEffect(() => {
    const currentFiles = mockFileSystem[currentPath] || []
    setFiles(currentFiles)
  }, [currentPath])

  // Cursor blinking effect
  useEffect(() => {
    const interval = setInterval(() => {
      setShowCursor(prev => !prev)
    }, 500)
    return () => clearInterval(interval)
  }, [])

  // Simulate time updates when playing
  useEffect(() => {
    if (isPlaying && currentTrack) {
      const interval = setInterval(() => {
        setProgress(prev => {
          const newProgress = prev + 1
          if (newProgress >= 100) {
            setIsPlaying(false)
            return 0
          }
          return newProgress
        })

        // Update current time display
        const minutes = Math.floor((progress / 100) * 240 / 60)
        const seconds = Math.floor((progress / 100) * 240 % 60)
        setCurrentTime(`${minutes}:${seconds.toString().padStart(2, '0')}`)
      }, 1000)

      return () => clearInterval(interval)
    }
  }, [isPlaying, currentTrack, progress])

  const handleCommand = (cmd: string) => {
    const newOutput = [...terminalOutput, `> ${cmd}`]

    switch (cmd.toLowerCase().trim()) {
      case 'help':
        newOutput.push('Available commands:')
        newOutput.push('  play [track_id] - Play a track')
        newOutput.push('  pause - Pause playback')
        newOutput.push('  stop - Stop playback')
        newOutput.push('  list - Show playlist')
        newOutput.push('  volume [0-100] - Set volume')
        newOutput.push('  browse - Open file browser')
        newOutput.push('  cd [path] - Change directory')
        newOutput.push('  ls - List files in current directory')
        newOutput.push('  clear - Clear terminal')
        break
      case 'browse':
        setShowFileBrowser(true)
        newOutput.push('File browser opened')
        break
      case 'ls':
        newOutput.push(`Files in ${currentPath}:`)
        files.forEach(file => {
          const icon = file.isDirectory ? '📁' : (file.isAudio ? '🎵' : '📄')
          newOutput.push(`  ${icon} ${file.name}`)
        })
        break
      case 'play':
        if (currentTrack) {
          setIsPlaying(true)
          newOutput.push(`Playing: ${currentTrack.title} by ${currentTrack.artist}`)
        } else {
          setCurrentTrack(playlist[0])
          setIsPlaying(true)
          newOutput.push(`Playing: ${playlist[0].title} by ${playlist[0].artist}`)
        }
        break
      case 'pause':
        setIsPlaying(false)
        newOutput.push('Playback paused')
        break
      case 'stop':
        setIsPlaying(false)
        setProgress(0)
        setCurrentTime('0:00')
        newOutput.push('Playback stopped')
        break
      case 'list':
        newOutput.push('Playlist:')
        playlist.forEach((track, index) => {
          newOutput.push(`  ${index + 1}. ${track.title} - ${track.artist} (${track.duration})`)
        })
        break
      case 'clear':
        setTerminalOutput(['> Terminal cleared'])
        setCommand('')
        return
      default:
        if (cmd.startsWith('play ')) {
          const trackId = cmd.split(' ')[1]
          const track = playlist.find(t => t.id === trackId) || playlist[parseInt(trackId) - 1]
          if (track) {
            setCurrentTrack(track)
            setIsPlaying(true)
            setProgress(0)
            newOutput.push(`Playing: ${track.title} by ${track.artist}`)
          } else {
            newOutput.push('Track not found')
          }
        } else if (cmd.startsWith('volume ')) {
          const vol = parseInt(cmd.split(' ')[1])
          if (vol >= 0 && vol <= 100) {
            setVolume(vol)
            newOutput.push(`Volume set to ${vol}%`)
          } else {
            newOutput.push('Volume must be between 0 and 100')
          }
        } else if (cmd.startsWith('cd ')) {
          const newPath = cmd.substring(3).trim()
          if (mockFileSystem[newPath]) {
            setCurrentPath(newPath)
            newOutput.push(`Changed directory to ${newPath}`)
          } else {
            newOutput.push(`Directory not found: ${newPath}`)
          }
        } else if (cmd.trim()) {
          newOutput.push(`Command not found: ${cmd}`)
        }
    }

    setTerminalOutput(newOutput)
    setCommand('')
  }

  return (
    <div
      className="w-full h-full font-mono flex flex-col rounded-lg overflow-hidden shadow-2xl"
      style={{
        background: 'linear-gradient(135deg, #4A1E1A 0%, #3A1614 100%)',
        color: '#F5F0E8',
        border: '1px solid #8B3A32'
      }}
    >
      {/* Beautiful Header */}
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        className="backdrop-blur-sm border-b px-6 py-4"
        style={{
          background: 'rgba(229, 90, 79, 0.15)',
          borderColor: '#8B3A32'
        }}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div
              className="font-bold text-2xl"
              style={{ color: '#FF9F5A' }}
            >
              echo
            </div>
            <div
              className="text-sm opacity-80"
              style={{ color: '#D4A574' }}
            >
              beautiful music player
            </div>
          </div>
          <div
            className="text-xs opacity-70"
            style={{ color: '#D4A574' }}
          >
            {new Date().toLocaleTimeString()}
          </div>
        </div>
      </motion.div>

      <div className="flex-1 flex">
        {/* Main Terminal Area */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="flex-1 p-6 overflow-y-auto"
        >
          {/* Terminal Output */}
          <div className="space-y-1 mb-6">
            <AnimatePresence>
              {terminalOutput.map((line, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.03 }}
                  className="text-sm"
                  style={{
                    color: line.startsWith('>')
                      ? '#FF9F5A'
                      : line.includes('error') || line.includes('not found')
                      ? '#ff6b6b'
                      : line.includes('Playing:') || line.includes('Volume set') || line.includes('📁') || line.includes('🎵')
                      ? '#FF9F5A'
                      : '#D4A574'
                  }}
                >
                  {line}
                </motion.div>
              ))}
            </AnimatePresence>
          </div>

          {/* Command Input */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="flex items-center rounded-lg px-4 py-3 border"
            style={{
              background: 'rgba(229, 90, 79, 0.1)',
              borderColor: '#8B3A32'
            }}
          >
            <span
              className="mr-3 font-bold"
              style={{ color: '#FF9F5A' }}
            >
              {currentPath}$
            </span>
            <input
              type="text"
              value={command}
              onChange={(e) => setCommand(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleCommand(command)}
              className="bg-transparent border-none outline-none flex-1"
              style={{
                color: '#F5F0E8'
              }}
              placeholder="enter command..."
              autoFocus
            />
            <span
              className={`w-2 h-4 ml-2 rounded-sm ${showCursor ? 'opacity-100' : 'opacity-0'} transition-opacity`}
              style={{ backgroundColor: '#FF9F5A' }}
            ></span>
          </motion.div>

          {/* File Browser */}
          {showFileBrowser && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-6 rounded-lg p-4 border"
              style={{
                background: 'rgba(229, 90, 79, 0.1)',
                borderColor: '#8B3A32'
              }}
            >
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                  <Folder size={20} style={{ color: '#FF9F5A' }} />
                  <span style={{ color: '#F5F0E8' }}>{currentPath}</span>
                </div>
                <button
                  onClick={() => setShowFileBrowser(false)}
                  className="px-3 py-1 rounded text-sm"
                  style={{
                    background: '#8B3A32',
                    color: '#F5F0E8'
                  }}
                >
                  Close
                </button>
              </div>

              <div className="space-y-2 max-h-40 overflow-y-auto">
                {files.map((file, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className="flex items-center space-x-3 p-2 rounded cursor-pointer hover:bg-opacity-20"
                    style={{
                      color: file.isAudio ? '#FF9F5A' : '#D4A574'
                    }}
                    onClick={() => {
                      if (file.isDirectory) {
                        setCurrentPath(file.path)
                      } else if (file.isAudio) {
                        const newTrack: Track = {
                          id: Date.now().toString(),
                          title: file.name.replace('.mp3', ''),
                          artist: 'Unknown Artist',
                          duration: '3:45',
                          file: file.path
                        }
                        setCurrentTrack(newTrack)
                        setIsPlaying(true)
                        setShowFileBrowser(false)
                      }
                    }}
                  >
                    {file.isDirectory ? (
                      <Folder size={16} />
                    ) : file.isAudio ? (
                      <Music size={16} />
                    ) : (
                      <div className="w-4 h-4" />
                    )}
                    <span className="text-sm">{file.name}</span>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}

          {/* Now Playing Info */}
          {currentTrack && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-6 rounded-lg p-6 border"
              style={{
                background: 'rgba(229, 90, 79, 0.15)',
                borderColor: '#8B3A32'
              }}
            >
              <div className="flex items-center space-x-4">
                <div
                  className="w-16 h-16 rounded-lg flex items-center justify-center"
                  style={{ background: 'rgba(255, 159, 90, 0.2)' }}
                >
                  <Volume2 size={24} style={{ color: '#FF9F5A' }} />
                </div>
                <div className="flex-1">
                  <h3
                    className="font-semibold text-lg"
                    style={{ color: '#F5F0E8' }}
                  >
                    {currentTrack.title}
                  </h3>
                  <p style={{ color: '#D4A574' }}>{currentTrack.artist}</p>
                  <div className="mt-2 flex items-center space-x-2">
                    <span
                      className="text-xs"
                      style={{ color: '#D4A574' }}
                    >
                      {currentTime}
                    </span>
                    <div
                      className="flex-1 rounded-full h-1"
                      style={{ background: 'rgba(139, 58, 50, 0.3)' }}
                    >
                      <motion.div
                        className="h-1 rounded-full"
                        style={{ background: '#FF9F5A' }}
                        initial={{ width: 0 }}
                        animate={{ width: `${progress}%` }}
                        transition={{ duration: 0.5 }}
                      />
                    </div>
                    <span
                      className="text-xs"
                      style={{ color: '#D4A574' }}
                    >
                      {currentTrack.duration || totalTime}
                    </span>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setIsPlaying(!isPlaying)}
                    className="w-12 h-12 rounded-full flex items-center justify-center transition-colors"
                    style={{
                      background: '#FF9F5A',
                      color: '#4A1E1A'
                    }}
                  >
                    {isPlaying ? <Pause size={20} /> : <Play size={20} />}
                  </motion.button>
                </div>
              </div>
            </motion.div>
          )}

          {/* Volume Control */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="mt-6 flex items-center space-x-3 rounded-lg px-4 py-3 border"
            style={{
              background: 'rgba(229, 90, 79, 0.1)',
              borderColor: '#8B3A32'
            }}
          >
            <Volume2 size={16} style={{ color: '#D4A574' }} />
            <div
              className="flex-1 rounded-full h-2"
              style={{ background: 'rgba(139, 58, 50, 0.3)' }}
            >
              <div
                className="h-2 rounded-full transition-all duration-200"
                style={{
                  width: `${volume}%`,
                  background: '#FF9F5A'
                }}
              />
            </div>
            <span
              className="text-sm w-10"
              style={{ color: '#D4A574' }}
            >
              {volume}%
            </span>
          </motion.div>
        </motion.div>
      </div>
    </div>
  )
}

export default App
