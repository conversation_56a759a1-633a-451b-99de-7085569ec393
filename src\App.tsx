import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Play, Pause, Volume2 } from 'lucide-react'
import './App.css'

interface Track {
  id: string
  title: string
  artist: string
  duration: string
  file?: string
}

function App() {
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTrack, setCurrentTrack] = useState<Track | null>(null)
  const [volume, setVolume] = useState(75)
  const [currentTime, setCurrentTime] = useState('0:00')
  const [totalTime] = useState('0:00')
  const [progress, setProgress] = useState(0)
  const [terminalOutput, setTerminalOutput] = useState<string[]>([
    '> echo v1.0.0 - beautiful music player',
    '> type "help" for available commands',
    '> press alt+pageup to toggle window'
  ])
  const [command, setCommand] = useState('')
  const [showCursor, setShowCursor] = useState(true)

  // Sample tracks
  const [playlist] = useState<Track[]>([
    { id: '1', title: 'Synthwave Dreams', artist: 'Neon Rider', duration: '3:45' },
    { id: '2', title: 'Digital Horizon', artist: 'Cyber Echo', duration: '4:12' },
    { id: '3', title: 'Terminal Blues', artist: 'Code Runner', duration: '2:58' },
    { id: '4', title: 'Binary Sunset', artist: 'Data Stream', duration: '5:23' },
  ])

  // Cursor blinking effect
  useEffect(() => {
    const interval = setInterval(() => {
      setShowCursor(prev => !prev)
    }, 500)
    return () => clearInterval(interval)
  }, [])

  // Simulate time updates when playing
  useEffect(() => {
    if (isPlaying && currentTrack) {
      const interval = setInterval(() => {
        setProgress(prev => {
          const newProgress = prev + 1
          if (newProgress >= 100) {
            setIsPlaying(false)
            return 0
          }
          return newProgress
        })

        // Update current time display
        const minutes = Math.floor((progress / 100) * 240 / 60)
        const seconds = Math.floor((progress / 100) * 240 % 60)
        setCurrentTime(`${minutes}:${seconds.toString().padStart(2, '0')}`)
      }, 1000)

      return () => clearInterval(interval)
    }
  }, [isPlaying, currentTrack, progress])

  const handleCommand = (cmd: string) => {
    const newOutput = [...terminalOutput, `> ${cmd}`]

    switch (cmd.toLowerCase().trim()) {
      case 'help':
        newOutput.push('Available commands:')
        newOutput.push('  play [track_id] - Play a track')
        newOutput.push('  pause - Pause playback')
        newOutput.push('  stop - Stop playback')
        newOutput.push('  list - Show playlist')
        newOutput.push('  volume [0-100] - Set volume')
        newOutput.push('  clear - Clear terminal')
        break
      case 'play':
        if (currentTrack) {
          setIsPlaying(true)
          newOutput.push(`Playing: ${currentTrack.title} by ${currentTrack.artist}`)
        } else {
          setCurrentTrack(playlist[0])
          setIsPlaying(true)
          newOutput.push(`Playing: ${playlist[0].title} by ${playlist[0].artist}`)
        }
        break
      case 'pause':
        setIsPlaying(false)
        newOutput.push('Playback paused')
        break
      case 'stop':
        setIsPlaying(false)
        setProgress(0)
        setCurrentTime('0:00')
        newOutput.push('Playback stopped')
        break
      case 'list':
        newOutput.push('Playlist:')
        playlist.forEach((track, index) => {
          newOutput.push(`  ${index + 1}. ${track.title} - ${track.artist} (${track.duration})`)
        })
        break
      case 'clear':
        setTerminalOutput(['> Terminal cleared'])
        setCommand('')
        return
      default:
        if (cmd.startsWith('play ')) {
          const trackId = cmd.split(' ')[1]
          const track = playlist.find(t => t.id === trackId) || playlist[parseInt(trackId) - 1]
          if (track) {
            setCurrentTrack(track)
            setIsPlaying(true)
            setProgress(0)
            newOutput.push(`Playing: ${track.title} by ${track.artist}`)
          } else {
            newOutput.push('Track not found')
          }
        } else if (cmd.startsWith('volume ')) {
          const vol = parseInt(cmd.split(' ')[1])
          if (vol >= 0 && vol <= 100) {
            setVolume(vol)
            newOutput.push(`Volume set to ${vol}%`)
          } else {
            newOutput.push('Volume must be between 0 and 100')
          }
        } else if (cmd.trim()) {
          newOutput.push(`Command not found: ${cmd}`)
        }
    }

    setTerminalOutput(newOutput)
    setCommand('')
  }

  return (
    <div className="w-full h-full bg-echo-bg text-echo-text font-mono flex flex-col rounded-lg overflow-hidden shadow-2xl backdrop-blur-sm">
      {/* Minimal Header */}
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-echo-surface/20 backdrop-blur-sm border-b border-echo-border/30 px-6 py-3"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="text-echo-accent font-bold text-lg">echo</div>
            <div className="text-echo-muted text-sm">music player</div>
          </div>
          <div className="text-echo-muted text-xs">
            {new Date().toLocaleTimeString()}
          </div>
        </div>
      </motion.div>

      <div className="flex-1 flex">
        {/* Main Terminal Area */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="flex-1 p-6 overflow-y-auto"
        >
          {/* Terminal Output */}
          <div className="space-y-1 mb-6">
            <AnimatePresence>
              {terminalOutput.map((line, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.03 }}
                  className={`text-sm ${
                    line.startsWith('>')
                      ? 'text-echo-accent'
                      : line.includes('error') || line.includes('not found')
                      ? 'text-red-400'
                      : line.includes('Playing:') || line.includes('Volume set')
                      ? 'text-echo-accent'
                      : 'text-echo-muted'
                  }`}
                >
                  {line}
                </motion.div>
              ))}
            </AnimatePresence>
          </div>

          {/* Command Input */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="flex items-center bg-echo-surface/10 rounded-lg px-4 py-3 border border-echo-border/20"
          >
            <span className="text-echo-accent mr-3 font-bold">$</span>
            <input
              type="text"
              value={command}
              onChange={(e) => setCommand(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleCommand(command)}
              className="bg-transparent border-none outline-none flex-1 text-echo-text placeholder-echo-muted"
              placeholder="enter command..."
              autoFocus
            />
            <span className={`w-2 h-4 bg-echo-accent ml-2 rounded-sm ${showCursor ? 'opacity-100' : 'opacity-0'} transition-opacity`}></span>
          </motion.div>

          {/* Now Playing Info */}
          {currentTrack && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-8 bg-echo-surface/10 rounded-lg p-6 border border-echo-border/20"
            >
              <div className="flex items-center space-x-4">
                <div className="w-16 h-16 bg-echo-accent/20 rounded-lg flex items-center justify-center">
                  <Volume2 size={24} className="text-echo-accent" />
                </div>
                <div className="flex-1">
                  <h3 className="text-echo-text font-semibold text-lg">{currentTrack.title}</h3>
                  <p className="text-echo-muted">{currentTrack.artist}</p>
                  <div className="mt-2 flex items-center space-x-2">
                    <span className="text-xs text-echo-muted">{currentTime}</span>
                    <div className="flex-1 bg-echo-border/30 rounded-full h-1">
                      <motion.div
                        className="bg-echo-accent h-1 rounded-full"
                        initial={{ width: 0 }}
                        animate={{ width: `${progress}%` }}
                        transition={{ duration: 0.5 }}
                      />
                    </div>
                    <span className="text-xs text-echo-muted">{currentTrack.duration || totalTime}</span>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setIsPlaying(!isPlaying)}
                    className="w-12 h-12 rounded-full bg-echo-accent text-echo-bg flex items-center justify-center hover:bg-echo-peach transition-colors"
                  >
                    {isPlaying ? <Pause size={20} /> : <Play size={20} />}
                  </motion.button>
                </div>
              </div>
            </motion.div>
          )}

          {/* Volume Control */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="mt-6 flex items-center space-x-3 bg-echo-surface/10 rounded-lg px-4 py-3 border border-echo-border/20"
          >
            <Volume2 size={16} className="text-echo-muted" />
            <div className="flex-1 bg-echo-border/30 rounded-full h-2">
              <div
                className="bg-echo-accent h-2 rounded-full transition-all duration-200"
                style={{ width: `${volume}%` }}
              />
            </div>
            <span className="text-sm text-echo-muted w-10">{volume}%</span>
          </motion.div>
        </motion.div>
      </div>
    </div>
  )
}

export default App
