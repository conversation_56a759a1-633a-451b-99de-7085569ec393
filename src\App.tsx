import { useState, useEffect } from 'react'
import { invoke } from '@tauri-apps/api/core'
import { listen } from '@tauri-apps/api/event'
import './App.css'

interface Track {
  id: string
  title: string
  artist: string
  duration: string
  file_path: string
}



function App() {
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTrack, setCurrentTrack] = useState<Track | null>(null)
  const [volume, setVolume] = useState(75)
  const [currentTime, setCurrentTime] = useState('0:00')

  const [progress, setProgress] = useState(0)
  const [terminalOutput, setTerminalOutput] = useState<string[]>([
    'echo v1.0.0 - terminal music player',
    'scanning audio files...'
  ])
  const [command, setCommand] = useState('')
  const [showCursor, setShowCursor] = useState(true)

  // Audio files state
  const [audioFiles, setAudioFiles] = useState<Track[]>([])
  const [selectedIndex, setSelectedIndex] = useState(0)


  // Scan for real audio files
  useEffect(() => {
    const scanFiles = async () => {
      try {
        const files = await invoke<Track[]>('scan_audio_files')
        setAudioFiles(files)
        setTerminalOutput(prev => [
          ...prev,
          `found ${files.length} audio files`,
          'hotkeys: ctrl+alt+e (toggle), ctrl+alt+space (play/pause)',
          'ctrl+alt+arrows (next/prev/vol), type "help" for commands'
        ])
      } catch (error) {
        setTerminalOutput(prev => [
          ...prev,
          'error scanning files: ' + error,
          'type "help" for commands'
        ])
      }
    }
    scanFiles()
  }, [])

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.key === 'ArrowUp' && selectedIndex > 0) {
        setSelectedIndex(selectedIndex - 1)
      } else if (e.key === 'ArrowDown' && selectedIndex < audioFiles.length - 1) {
        setSelectedIndex(selectedIndex + 1)
      } else if (e.key === 'Enter') {
        const selectedTrack = audioFiles[selectedIndex]
        if (selectedTrack) {
          setCurrentTrack(selectedTrack)
          setIsPlaying(true)
          setProgress(0)
        }
      }
    }

    window.addEventListener('keydown', handleKeyPress)
    return () => window.removeEventListener('keydown', handleKeyPress)
  }, [selectedIndex, audioFiles])

  // Global hotkey listeners
  useEffect(() => {
    const setupHotkeyListeners = async () => {
      await listen('hotkey', (event) => {
        const action = event.payload as string

        switch (action) {
          case 'play_pause':
            setIsPlaying(!isPlaying)
            break
          case 'next':
            if (audioFiles.length > 0) {
              const nextIndex = (selectedIndex + 1) % audioFiles.length
              setSelectedIndex(nextIndex)
              setCurrentTrack(audioFiles[nextIndex])
              setIsPlaying(true)
              setProgress(0)
            }
            break
          case 'prev':
            if (audioFiles.length > 0) {
              const prevIndex = selectedIndex === 0 ? audioFiles.length - 1 : selectedIndex - 1
              setSelectedIndex(prevIndex)
              setCurrentTrack(audioFiles[prevIndex])
              setIsPlaying(true)
              setProgress(0)
            }
            break
          case 'vol_up':
            setVolume(prev => Math.min(100, prev + 10))
            break
          case 'vol_down':
            setVolume(prev => Math.max(0, prev - 10))
            break
        }
      })
    }

    setupHotkeyListeners()
  }, [isPlaying, selectedIndex, audioFiles])

  // Cursor blinking effect
  useEffect(() => {
    const interval = setInterval(() => {
      setShowCursor(prev => !prev)
    }, 500)
    return () => clearInterval(interval)
  }, [])

  // Simulate time updates when playing
  useEffect(() => {
    if (isPlaying && currentTrack) {
      const interval = setInterval(() => {
        setProgress(prev => {
          const newProgress = prev + 1
          if (newProgress >= 100) {
            setIsPlaying(false)
            return 0
          }
          return newProgress
        })

        // Update current time display
        const minutes = Math.floor((progress / 100) * 240 / 60)
        const seconds = Math.floor((progress / 100) * 240 % 60)
        setCurrentTime(`${minutes}:${seconds.toString().padStart(2, '0')}`)
      }, 1000)

      return () => clearInterval(interval)
    }
  }, [isPlaying, currentTrack, progress])

  const handleCommand = (cmd: string) => {
    const newOutput = [...terminalOutput, `$ ${cmd}`]

    switch (cmd.toLowerCase().trim()) {
      case 'help':
        newOutput.push('commands:')
        newOutput.push('  list - show audio files')
        newOutput.push('  play [id] - play track by id')
        newOutput.push('  pause - pause playback')
        newOutput.push('  stop - stop playback')
        newOutput.push('  vol [0-100] - set volume')
        newOutput.push('  next - next track')
        newOutput.push('  prev - previous track')
        newOutput.push('  clear - clear screen')
        break
      case 'list':
        newOutput.push('audio files:')
        audioFiles.forEach((track, index) => {
          const status = currentTrack?.id === track.id ? (isPlaying ? '▶' : '⏸') : ' '
          const selected = index === selectedIndex ? '>' : ' '
          newOutput.push(`${selected}${status} ${index + 1}. ${track.title} ${track.duration}`)
        })
        newOutput.push('use arrow keys + enter to select')
        break
      case 'play':
        if (audioFiles.length > 0) {
          const track = audioFiles[selectedIndex]
          setCurrentTrack(track)
          setIsPlaying(true)
          setProgress(0)
          newOutput.push(`playing: ${track.title}`)
        }
        break
      case 'pause':
        setIsPlaying(false)
        newOutput.push('paused')
        break
      case 'stop':
        setIsPlaying(false)
        setProgress(0)
        setCurrentTime('0:00')
        newOutput.push('stopped')
        break
      case 'next':
        if (audioFiles.length > 0) {
          const nextIndex = (selectedIndex + 1) % audioFiles.length
          setSelectedIndex(nextIndex)
          const track = audioFiles[nextIndex]
          setCurrentTrack(track)
          setIsPlaying(true)
          setProgress(0)
          newOutput.push(`next: ${track.title}`)
        }
        break
      case 'prev':
        if (audioFiles.length > 0) {
          const prevIndex = selectedIndex === 0 ? audioFiles.length - 1 : selectedIndex - 1
          setSelectedIndex(prevIndex)
          const track = audioFiles[prevIndex]
          setCurrentTrack(track)
          setIsPlaying(true)
          setProgress(0)
          newOutput.push(`prev: ${track.title}`)
        }
        break
      case 'clear':
        setTerminalOutput(['echo v1.0.0 - terminal music player'])
        setCommand('')
        return
      default:
        if (cmd.startsWith('play ')) {
          const trackId = parseInt(cmd.split(' ')[1]) - 1
          if (trackId >= 0 && trackId < audioFiles.length) {
            const track = audioFiles[trackId]
            setCurrentTrack(track)
            setIsPlaying(true)
            setProgress(0)
            setSelectedIndex(trackId)
            newOutput.push(`playing: ${track.title}`)
          } else {
            newOutput.push('invalid track id')
          }
        } else if (cmd.startsWith('vol ')) {
          const vol = parseInt(cmd.split(' ')[1])
          if (vol >= 0 && vol <= 100) {
            setVolume(vol)
            newOutput.push(`volume: ${vol}%`)
          } else {
            newOutput.push('volume: 0-100')
          }
        } else if (cmd.trim()) {
          newOutput.push(`unknown command: ${cmd}`)
        }
    }

    setTerminalOutput(newOutput)
    setCommand('')
  }

  return (
    <div
      className="w-full h-full font-mono flex flex-col"
      style={{
        background: '#4A1E1A', // Burgundy background
        color: '#F5F0E8', // Cream text
        padding: '16px',
        // 120fps optimizations
        willChange: 'transform',
        transform: 'translateZ(0)',
        backfaceVisibility: 'hidden'
      }}
    >
      {/* Terminal Header */}
      <div
        className="mb-4 pb-2 border-b"
        style={{
          borderColor: '#E85A4F', // Coral border
          color: '#FF9F5A' // Peach title
        }}
      >
        echo@terminal:~$ music player v1.0.0
      </div>

      {/* Terminal Output */}
      <div
        className="flex-1 mb-4"
        style={{
          overflowY: 'hidden',
          height: 'calc(100vh - 200px)'
        }}
      >
        <div
          className="h-full"
          style={{
            overflowY: 'scroll',
            scrollbarWidth: 'none',
            msOverflowStyle: 'none'
          }}
        >
          {terminalOutput.slice(-50).map((line, index) => (
            <div
              key={index}
              className="font-mono text-sm leading-tight"
              style={{
                color: line.startsWith('$')
                  ? '#FF9F5A' // Peach for commands
                  : line.includes('playing:') || line.includes('paused') || line.includes('stopped')
                  ? '#E85A4F' // Coral for status
                  : line.includes('▶') || line.includes('⏸')
                  ? '#FF9F5A' // Peach for play indicators
                  : line.includes('>')
                  ? '#F5F0E8' // Cream for selected
                  : line.includes('commands:') || line.includes('audio files:')
                  ? '#D4A574' // Muted for headers
                  : line.includes('unknown') || line.includes('invalid') || line.includes('error')
                  ? '#E85A4F' // Coral for errors
                  : '#F5F0E8' // Cream for normal text
              }}
            >
              {line}
            </div>
          ))}
        </div>
      </div>

      {/* Status Line */}
      {currentTrack && (
        <div
          className="mb-2 font-mono text-sm"
          style={{ color: '#E85A4F' }} // Coral for status
        >
          {isPlaying ? '▶' : '⏸'} {currentTrack.title} [{currentTime}/{currentTrack.duration}] vol:{volume}%
        </div>
      )}

      {/* Command Input */}
      <div className="flex items-center">
        <span
          className="mr-2 font-mono"
          style={{ color: '#FF9F5A' }} // Peach for prompt
        >
          $
        </span>
        <input
          type="text"
          value={command}
          onChange={(e) => setCommand(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && handleCommand(command)}
          className="bg-transparent border-none outline-none flex-1 font-mono"
          style={{
            color: '#F5F0E8' // Cream for input text
          }}
          autoFocus
        />
        <span
          className={`w-2 h-4 ml-1 ${showCursor ? 'opacity-100' : 'opacity-0'} transition-opacity`}
          style={{ backgroundColor: '#FF9F5A' }} // Peach cursor
        >
          █
        </span>
      </div>
    </div>
  )
}

export default App
