mod audio;

use tauri::WindowEvent;

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
  tauri::Builder::default()
    .plugin(tauri_plugin_fs::init())
    .plugin(tauri_plugin_dialog::init())
    .setup(|app| {
      if cfg!(debug_assertions) {
        app.handle().plugin(
          tauri_plugin_log::Builder::default()
            .level(log::LevelFilter::Info)
            .build(),
        )?;
      }

      println!("Echo music player started successfully!");

      Ok(())
    })
    .on_window_event(|window, event| {
      match event {
        WindowEvent::CloseRequested { api, .. } => {
          // Hide window instead of closing
          window.hide().unwrap();
          api.prevent_close();
        }
        _ => {}
      }
    })
    .invoke_handler(tauri::generate_handler![
      audio::play_audio,
      audio::pause_audio,
      audio::resume_audio,
      audio::stop_audio,
      audio::set_volume,
      audio::get_player_state,
      audio::set_current_track,
      get_system_info,
      open_file_dialog,
      toggle_window
    ])
    .run(tauri::generate_context!())
    .expect("error while running tauri application");
}

#[tauri::command]
async fn get_system_info() -> Result<String, String> {
  Ok(format!("Music Player Terminal v0.1.0 - Rust Backend"))
}

#[tauri::command]
async fn open_file_dialog() -> Result<Option<String>, String> {
  // This would be implemented with the dialog plugin
  // For now, return a placeholder
  Ok(None)
}

#[tauri::command]
async fn toggle_window(window: tauri::Window) -> Result<(), String> {
  let is_visible = window.is_visible().map_err(|e| e.to_string())?;
  if is_visible {
    window.hide().map_err(|e| e.to_string())?;
  } else {
    window.show().map_err(|e| e.to_string())?;
    window.set_focus().map_err(|e| e.to_string())?;
  }
  Ok(())
}
