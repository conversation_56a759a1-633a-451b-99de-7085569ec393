mod audio;

use tauri::{<PERSON>, WindowEvent, Emitter};
use global_hotkey::{GlobalHotKeyManager, hotkey::{HotKey, Modifiers, Code}, GlobalHotKeyEvent};

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
  tauri::Builder::default()
    .plugin(tauri_plugin_fs::init())
    .plugin(tauri_plugin_dialog::init())
    .setup(|app| {
      if cfg!(debug_assertions) {
        app.handle().plugin(
          tauri_plugin_log::Builder::default()
            .level(log::LevelFilter::Info)
            .build(),
        )?;
      }

      // Set up global hotkeys
      let app_handle = app.handle().clone();
      std::thread::spawn(move || {
        let manager = GlobalHotKeyManager::new().unwrap();

        // Main toggle hotkey: Ctrl+Alt+E
        let toggle_hotkey = HotKey::new(Some(Modifiers::CONTROL | Modifiers::ALT), Code::KeyE);
        // Media hotkeys
        let play_pause_hotkey = HotKey::new(Some(Modifiers::CONTROL | Modifiers::ALT), Code::Space);
        let next_hotkey = HotKey::new(Some(Modifiers::CONTROL | Modifiers::ALT), Code::ArrowRight);
        let prev_hotkey = HotKey::new(Some(Modifiers::CONTROL | Modifiers::ALT), Code::ArrowLeft);
        let vol_up_hotkey = HotKey::new(Some(Modifiers::CONTROL | Modifiers::ALT), Code::ArrowUp);
        let vol_down_hotkey = HotKey::new(Some(Modifiers::CONTROL | Modifiers::ALT), Code::ArrowDown);

        let hotkeys = vec![
          (toggle_hotkey, "toggle"),
          (play_pause_hotkey, "play_pause"),
          (next_hotkey, "next"),
          (prev_hotkey, "prev"),
          (vol_up_hotkey, "vol_up"),
          (vol_down_hotkey, "vol_down"),
        ];

        for (hotkey, name) in &hotkeys {
          if let Err(e) = manager.register(*hotkey) {
            eprintln!("Failed to register {} hotkey: {}", name, e);
          } else {
            println!("Registered hotkey: {}", name);
          }
        }

        let receiver = GlobalHotKeyEvent::receiver();
        loop {
          if let Ok(event) = receiver.recv() {
            if let Some(window) = app_handle.get_webview_window("main") {
              // Determine which hotkey was pressed
              if event.id == toggle_hotkey.id() {
                let _ = window.is_visible().map(|visible| {
                  if visible {
                    let _ = window.hide();
                  } else {
                    let _ = window.show();
                    let _ = window.set_focus();
                  }
                });
              } else if event.id == play_pause_hotkey.id() {
                let _ = window.emit("hotkey", "play_pause");
              } else if event.id == next_hotkey.id() {
                let _ = window.emit("hotkey", "next");
              } else if event.id == prev_hotkey.id() {
                let _ = window.emit("hotkey", "prev");
              } else if event.id == vol_up_hotkey.id() {
                let _ = window.emit("hotkey", "vol_up");
              } else if event.id == vol_down_hotkey.id() {
                let _ = window.emit("hotkey", "vol_down");
              }
            }
          }
        }
      });

      println!("Echo music player started successfully!");

      Ok(())
    })
    .on_window_event(|window, event| {
      match event {
        WindowEvent::CloseRequested { api, .. } => {
          // Hide window instead of closing
          window.hide().unwrap();
          api.prevent_close();
        }
        _ => {}
      }
    })
    .invoke_handler(tauri::generate_handler![
      audio::play_audio,
      audio::pause_audio,
      audio::resume_audio,
      audio::stop_audio,
      audio::set_volume,
      audio::get_player_state,
      audio::set_current_track,
      audio::scan_audio_files,
      get_system_info,
      open_file_dialog,
      toggle_window
    ])
    .run(tauri::generate_context!())
    .expect("error while running tauri application");
}

#[tauri::command]
async fn get_system_info() -> Result<String, String> {
  Ok(format!("Music Player Terminal v0.1.0 - Rust Backend"))
}

#[tauri::command]
async fn open_file_dialog() -> Result<Option<String>, String> {
  // This would be implemented with the dialog plugin
  // For now, return a placeholder
  Ok(None)
}

#[tauri::command]
async fn toggle_window(window: tauri::Window) -> Result<(), String> {
  let is_visible = window.is_visible().map_err(|e| e.to_string())?;
  if is_visible {
    window.hide().map_err(|e| e.to_string())?;
  } else {
    window.show().map_err(|e| e.to_string())?;
    window.set_focus().map_err(|e| e.to_string())?;
  }
  Ok(())
}
