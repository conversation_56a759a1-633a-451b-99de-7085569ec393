Z:\musicplayer\src-tauri\target\debug\deps\app_lib.lib: src\lib.rs src\audio.rs Z:\musicplayer\src-tauri\target\debug\build\music-player-terminal-b7437ed56e220e69\out/45f1706e214e051e2ea25d5fcefb1d3a2ca8c5cc7ea0df00a57ce212cf0da8c5

Z:\musicplayer\src-tauri\target\debug\deps\app_lib.dll: src\lib.rs src\audio.rs Z:\musicplayer\src-tauri\target\debug\build\music-player-terminal-b7437ed56e220e69\out/45f1706e214e051e2ea25d5fcefb1d3a2ca8c5cc7ea0df00a57ce212cf0da8c5

Z:\musicplayer\src-tauri\target\debug\deps\libapp_lib.rlib: src\lib.rs src\audio.rs Z:\musicplayer\src-tauri\target\debug\build\music-player-terminal-b7437ed56e220e69\out/45f1706e214e051e2ea25d5fcefb1d3a2ca8c5cc7ea0df00a57ce212cf0da8c5

Z:\musicplayer\src-tauri\target\debug\deps\app_lib.d: src\lib.rs src\audio.rs Z:\musicplayer\src-tauri\target\debug\build\music-player-terminal-b7437ed56e220e69\out/45f1706e214e051e2ea25d5fcefb1d3a2ca8c5cc7ea0df00a57ce212cf0da8c5

src\lib.rs:
src\audio.rs:
Z:\musicplayer\src-tauri\target\debug\build\music-player-terminal-b7437ed56e220e69\out/45f1706e214e051e2ea25d5fcefb1d3a2ca8c5cc7ea0df00a57ce212cf0da8c5:

# env-dep:CARGO_PKG_AUTHORS=Music Player Team
# env-dep:CARGO_PKG_DESCRIPTION=A high-performance terminal-style music player built with Tauri and Rust
# env-dep:CARGO_PKG_NAME=music-player-terminal
# env-dep:OUT_DIR=Z:\\musicplayer\\src-tauri\\target\\debug\\build\\music-player-terminal-b7437ed56e220e69\\out
