{"rustc": 10895048813736897673, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 16644688692125618460, "deps": [[2671782512663819132, "tauri_utils", false, 4392668422150800441], [3060637413840920116, "proc_macro2", false, 8319129890957314992], [3150220818285335163, "url", false, 13469117053291302290], [4899080583175475170, "semver", false, 226480500875875536], [4974441333307933176, "syn", false, 6333227840242989270], [7170110829644101142, "json_patch", false, 17354958663088037870], [7392050791754369441, "ico", false, 15243499029649061249], [8319709847752024821, "uuid", false, 14688885432787296528], [9556762810601084293, "brotli", false, 12141357743228960236], [9689903380558560274, "serde", false, 6070878912102744926], [9857275760291862238, "sha2", false, 5569933781831389136], [10806645703491011684, "thiserror", false, 8916265338513527458], [12687914511023397207, "png", false, 11075080443099168695], [13077212702700853852, "base64", false, 9195867271110172062], [15367738274754116744, "serde_json", false, 10067859309686628149], [15622660310229662834, "walkdir", false, 1189006774359693792], [17990358020177143287, "quote", false, 14364564765239372762]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-7e4ff510dac4a893\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}