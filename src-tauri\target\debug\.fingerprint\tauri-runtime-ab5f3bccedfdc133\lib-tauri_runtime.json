{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 15657897354478470176, "path": 4294496645477281794, "deps": [[2671782512663819132, "tauri_utils", false, 10398905973319311549], [3150220818285335163, "url", false, 10832840630612195651], [4143744114649553716, "raw_window_handle", false, 3983514741078308698], [6089812615193535349, "build_script_build", false, 9408925966437181308], [7606335748176206944, "dpi", false, 15335190195985147291], [9010263965687315507, "http", false, 4157821809111752408], [9689903380558560274, "serde", false, 15448259014108384113], [10806645703491011684, "thiserror", false, 8916265338513527458], [14585479307175734061, "windows", false, 1307616255356058140], [15367738274754116744, "serde_json", false, 17454827729379272329], [16727543399706004146, "cookie", false, 3431914073685546382]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-ab5f3bccedfdc133\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}