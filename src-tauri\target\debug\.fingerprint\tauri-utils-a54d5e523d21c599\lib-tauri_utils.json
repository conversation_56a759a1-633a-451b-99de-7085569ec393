{"rustc": 10895048813736897673, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 15657897354478470176, "path": 15206535318688532607, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 6377191626919846177], [3150220818285335163, "url", false, 10832840630612195651], [3191507132440681679, "serde_untagged", false, 2284014254567341348], [4899080583175475170, "semver", false, 7666702117649896626], [5578504951057029730, "serde_with", false, 1557224738307825375], [5986029879202738730, "log", false, 16174589429375859912], [6606131838865521726, "ctor", false, 11410305169889176729], [7170110829644101142, "json_patch", false, 139477641842313052], [8319709847752024821, "uuid", false, 2680089397691634165], [9010263965687315507, "http", false, 4157821809111752408], [9451456094439810778, "regex", false, 4585305480875941930], [9556762810601084293, "brotli", false, 12141357743228960236], [9689903380558560274, "serde", false, 15448259014108384113], [10806645703491011684, "thiserror", false, 8916265338513527458], [11989259058781683633, "dunce", false, 9940621396166974783], [13625485746686963219, "anyhow", false, 1292039079741882281], [15367738274754116744, "serde_json", false, 17454827729379272329], [15609422047640926750, "toml", false, 4888138687414066549], [15622660310229662834, "walkdir", false, 5020018030510933008], [15932120279885307830, "memchr", false, 15891093215126501800], [17146114186171651583, "infer", false, 14125365824362277325], [17155886227862585100, "glob", false, 13335192901601259105], [17186037756130803222, "phf", false, 14149974291060633986]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-a54d5e523d21c599\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}