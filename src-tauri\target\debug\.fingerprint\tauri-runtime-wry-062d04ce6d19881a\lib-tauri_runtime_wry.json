{"rustc": 10895048813736897673, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 15837875053910436589, "deps": [[376837177317575824, "softbuffer", false, 14797052775507681996], [2013030631243296465, "webview2_com", false, 9484889224176344146], [2671782512663819132, "tauri_utils", false, 10398905973319311549], [3150220818285335163, "url", false, 10832840630612195651], [3722963349756955755, "once_cell", false, 1839796023116530122], [4143744114649553716, "raw_window_handle", false, 3983514741078308698], [5986029879202738730, "log", false, 16174589429375859912], [6089812615193535349, "tauri_runtime", false, 17913610994767065956], [8826339825490770380, "tao", false, 5710486000295307948], [9010263965687315507, "http", false, 4157821809111752408], [9141053277961803901, "wry", false, 14392264562563017504], [11599800339996261026, "build_script_build", false, 5860446083109141776], [14585479307175734061, "windows", false, 1307616255356058140]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-062d04ce6d19881a\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}