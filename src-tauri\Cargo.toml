[package]
name = "music-player-terminal"
version = "0.1.0"
description = "A high-performance terminal-style music player built with <PERSON><PERSON> and <PERSON><PERSON>"
authors = ["Music Player Team"]
license = "MIT"
repository = ""
edition = "2021"
rust-version = "1.77.2"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
name = "app_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2.3.0" }

[dependencies]
serde_json = "1.0"
serde = { version = "1.0", features = ["derive"] }
log = "0.4"
tauri = { version = "2.6.2", features = ["protocol-asset"] }
tauri-plugin-log = "2"
tauri-plugin-fs = "2"
tauri-plugin-dialog = "2"
rodio = "0.19"
tokio = { version = "1.0", features = ["full"] }
anyhow = "1.0"
symphonia = { version = "0.5", features = ["all"] }
