{"rustc": 10895048813736897673, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 4391962897064323707, "deps": [[2671782512663819132, "tauri_utils", false, 4392668422150800441], [3060637413840920116, "proc_macro2", false, 8319129890957314992], [4974441333307933176, "syn", false, 6333227840242989270], [13077543566650298139, "heck", false, 7464608437653490905], [14455244907590647360, "tauri_codegen", false, 15310187635566877408], [17990358020177143287, "quote", false, 14364564765239372762]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-4911f8b247b11e03\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}