use serde::{Deserialize, Serialize};
use std::time::Duration;
use std::path::Path;
use std::fs;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Track {
    pub id: String,
    pub title: String,
    pub artist: String,
    pub duration: String,
    pub file_path: String,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct PlayerState {
    pub is_playing: bool,
    pub current_track: Option<Track>,
    pub volume: f32,
    pub position: Duration,
    pub duration: Duration,
}

// Simple mock audio player for now - we'll implement real audio later
#[derive(Debug, Clone)]
pub struct AudioPlayerState {
    pub is_playing: bool,
    pub current_track: Option<Track>,
    pub volume: f32,
    pub position: Duration,
    pub duration: Duration,
}

impl Default for AudioPlayerState {
    fn default() -> Self {
        Self {
            is_playing: false,
            current_track: None,
            volume: 0.75,
            position: Duration::from_secs(0),
            duration: Duration::from_secs(0),
        }
    }
}

// Tauri commands - simplified for now
#[tauri::command]
pub async fn play_audio(file_path: String) -> Result<String, String> {
    // For now, just return success - we'll implement real audio later
    Ok(format!("Playing: {}", file_path))
}

#[tauri::command]
pub async fn pause_audio() -> Result<String, String> {
    Ok("Audio paused".to_string())
}

#[tauri::command]
pub async fn resume_audio() -> Result<String, String> {
    Ok("Audio resumed".to_string())
}

#[tauri::command]
pub async fn stop_audio() -> Result<String, String> {
    Ok("Audio stopped".to_string())
}

#[tauri::command]
pub async fn set_volume(volume: f32) -> Result<String, String> {
    let clamped_volume = volume.clamp(0.0, 1.0);
    Ok(format!("Volume set to: {:.0}%", clamped_volume * 100.0))
}

#[tauri::command]
pub async fn get_player_state() -> Result<PlayerState, String> {
    Ok(PlayerState {
        is_playing: false,
        current_track: None,
        volume: 0.75,
        position: Duration::from_secs(0),
        duration: Duration::from_secs(0),
    })
}

#[tauri::command]
pub async fn set_current_track(track: Track) -> Result<String, String> {
    Ok(format!("Current track set to: {} by {}", track.title, track.artist))
}

#[tauri::command]
pub async fn scan_audio_files() -> Result<Vec<Track>, String> {
    let mut tracks = Vec::new();
    let audio_extensions = vec!["mp3", "wav", "flac", "ogg", "m4a", "aac"];

    // Common music directories to scan
    let music_dirs = vec![
        dirs::audio_dir(),
        dirs::home_dir().map(|p| p.join("Music")),
        dirs::desktop_dir().map(|p| p.join("Music")),
    ];

    for dir_option in music_dirs {
        if let Some(dir) = dir_option {
            if dir.exists() {
                scan_directory(&dir, &audio_extensions, &mut tracks)?;
            }
        }
    }

    // Also scan current directory
    let current_dir = std::env::current_dir().map_err(|e| e.to_string())?;
    scan_directory(&current_dir, &audio_extensions, &mut tracks)?;

    Ok(tracks)
}

fn scan_directory(dir: &Path, extensions: &[&str], tracks: &mut Vec<Track>) -> Result<(), String> {
    let entries = fs::read_dir(dir).map_err(|e| e.to_string())?;

    for entry in entries {
        let entry = entry.map_err(|e| e.to_string())?;
        let path = entry.path();

        if path.is_file() {
            if let Some(extension) = path.extension() {
                if let Some(ext_str) = extension.to_str() {
                    if extensions.contains(&ext_str.to_lowercase().as_str()) {
                        if let Some(file_name) = path.file_stem() {
                            if let Some(name_str) = file_name.to_str() {
                                let track = Track {
                                    id: tracks.len().to_string(),
                                    title: name_str.to_string(),
                                    artist: path.parent()
                                        .and_then(|p| p.file_name())
                                        .and_then(|n| n.to_str())
                                        .unwrap_or("Unknown")
                                        .to_string(),
                                    duration: "0:00".to_string(), // We'll implement duration detection later
                                    file_path: path.to_string_lossy().to_string(),
                                };
                                tracks.push(track);
                            }
                        }
                    }
                }
            }
        } else if path.is_dir() {
            // Recursively scan subdirectories (limit depth to avoid infinite loops)
            if let Some(dir_name) = path.file_name() {
                if let Some(name_str) = dir_name.to_str() {
                    // Skip hidden directories and system directories
                    if !name_str.starts_with('.') && !name_str.starts_with('$') {
                        scan_directory(&path, extensions, tracks)?;
                    }
                }
            }
        }
    }

    Ok(())
}
