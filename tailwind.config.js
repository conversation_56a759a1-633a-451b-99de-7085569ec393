/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        'mono': ['JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', 'monospace'],
      },
      colors: {
        echo: {
          // Based on the beautiful color palette provided
          cream: '#F5F0E8',      // Light cream background
          peach: '#FF9F5A',      // Warm orange/peach
          coral: '#E85A4F',      // Rich coral red
          burgundy: '#4A1E1A',   // Deep burgundy/brown
          // Derived colors for UI
          bg: '#4A1E1A',         // Main background (burgundy)
          surface: '#E85A4F',    // Surface elements (coral)
          accent: '#FF9F5A',     // Accent color (peach)
          text: '#F5F0E8',       // Primary text (cream)
          muted: '#D4A574',      // Muted text (between peach and cream)
          border: '#8B3A32',     // Borders (darker coral)
        }
      },
      animation: {
        'blink': 'blink 1s infinite',
        'glow': 'glow 2s ease-in-out infinite alternate',
        'slide-up': 'slideUp 0.3s ease-out',
        'slide-down': 'slideDown 0.3s ease-out',
        'fade-in': 'fadeIn 0.2s ease-out',
      },
      keyframes: {
        blink: {
          '0%, 50%': { opacity: '1' },
          '51%, 100%': { opacity: '0' },
        },
        glow: {
          '0%': { boxShadow: '0 0 5px #00ff00' },
          '100%': { boxShadow: '0 0 20px #00ff00, 0 0 30px #00ff00' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideDown: {
          '0%': { transform: 'translateY(-10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
      },
    },
  },
  plugins: [],
}
