{"rustc": 10895048813736897673, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 14943233685506182595, "deps": [[40386456601120721, "percent_encoding", false, 10390957414303225181], [1200537532907108615, "url<PERSON><PERSON>n", false, 6377191626919846177], [2013030631243296465, "webview2_com", false, 9484889224176344146], [2671782512663819132, "tauri_utils", false, 10398905973319311549], [3150220818285335163, "url", false, 10832840630612195651], [3331586631144870129, "getrandom", false, 3743412514901853904], [4143744114649553716, "raw_window_handle", false, 3983514741078308698], [4494683389616423722, "muda", false, 4432342788762775301], [4919829919303820331, "serialize_to_javascript", false, 7410655627124953577], [5986029879202738730, "log", false, 16174589429375859912], [6089812615193535349, "tauri_runtime", false, 17913610994767065956], [7573826311589115053, "tauri_macros", false, 10660101835517084538], [9010263965687315507, "http", false, 4157821809111752408], [9538054652646069845, "tokio", false, 17211858520132705060], [9689903380558560274, "serde", false, 15448259014108384113], [10229185211513642314, "mime", false, 1456957508286477648], [10806645703491011684, "thiserror", false, 8916265338513527458], [11599800339996261026, "tauri_runtime_wry", false, 5514530656903856446], [11989259058781683633, "dunce", false, 9940621396166974783], [12565293087094287914, "window_vibrancy", false, 9081739464085610069], [12986574360607194341, "serde_repr", false, 13751041575940833144], [13077543566650298139, "heck", false, 7464608437653490905], [13625485746686963219, "anyhow", false, 1292039079741882281], [14039947826026167952, "build_script_build", false, 2954686544370059593], [14585479307175734061, "windows", false, 1307616255356058140], [15367738274754116744, "serde_json", false, 17454827729379272329], [16928111194414003569, "dirs", false, 1663566858410743479], [17155886227862585100, "glob", false, 13335192901601259105]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-2bb94eee8b2cd412\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}