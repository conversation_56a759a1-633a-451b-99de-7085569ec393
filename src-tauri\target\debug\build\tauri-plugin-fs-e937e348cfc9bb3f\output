cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=Z:\musicplayer\src-tauri\target\debug\build\tauri-plugin-fs-e937e348cfc9bb3f\out\tauri-plugin-fs-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:GLOBAL_SCOPE_SCHEMA_PATH=Z:\musicplayer\src-tauri\target\debug\build\tauri-plugin-fs-e937e348cfc9bb3f\out\global-scope.json
cargo:GLOBAL_API_SCRIPT_PATH=\\?\C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tauri-plugin-fs-2.4.0\api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
