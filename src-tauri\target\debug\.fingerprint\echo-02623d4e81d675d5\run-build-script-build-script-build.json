{"rustc": 10895048813736897673, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10197015723251394741, "build_script_build", false, 9678153327165472723], [14039947826026167952, "build_script_build", false, 483295992832269931], [14525517306681678134, "build_script_build", false, 6904547613242718622], [6416823254013318197, "build_script_build", false, 8260309150381544382], [11457472658001459637, "build_script_build", false, 1744916563640105341], [8324462083842905811, "build_script_build", false, 17991843601342819907]], "local": [{"RerunIfChanged": {"output": "debug\\build\\echo-02623d4e81d675d5\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}