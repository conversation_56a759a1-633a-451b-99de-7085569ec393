use anyhow::Result;
use colored::*;
use dirs;
use rodio::{Decoder, OutputStream, Sink};
use std::{
    fs::File,
    io::{self, BufReader, Write},
    path::PathBuf,
};
use walkdir::WalkDir;

#[derive(Debug, Clone)]
struct Track {
    id: usize,
    title: String,
    artist: String,
    path: PathBuf,
    duration: String,
}

struct MusicPlayer {
    tracks: Vec<Track>,
    current_track: Option<usize>,
    selected_index: usize,
    is_playing: bool,
    volume: f32,
    sink: Option<Sink>,
    _stream: Option<OutputStream>,
}

impl MusicPlayer {
    fn new() -> Self {
        Self {
            tracks: Vec::new(),
            current_track: None,
            selected_index: 0,
            is_playing: false,
            volume: 0.7,
            sink: None,
            _stream: None,
        }
    }

    fn scan_audio_files(&mut self) -> Result<()> {
        println!("{}", "Scanning for audio files...".bright_yellow());
        
        let mut scan_dirs = Vec::new();
        
        // Add common music directories
        if let Some(music_dir) = dirs::audio_dir() {
            scan_dirs.push(music_dir);
        }
        if let Some(home_dir) = dirs::home_dir() {
            scan_dirs.push(home_dir.join("Music"));
        }
        if let Some(desktop_dir) = dirs::desktop_dir() {
            scan_dirs.push(desktop_dir.join("Music"));
        }
        
        // Add current directory
        scan_dirs.push(std::env::current_dir()?);
        
        let audio_extensions = ["mp3", "wav", "flac", "ogg", "m4a", "aac"];
        let mut track_id = 0;
        
        for dir in scan_dirs {
            if dir.exists() {
                for entry in WalkDir::new(&dir)
                    .max_depth(5)
                    .into_iter()
                    .filter_map(|e| e.ok())
                {
                    let path = entry.path();
                    if path.is_file() {
                        if let Some(extension) = path.extension() {
                            if let Some(ext_str) = extension.to_str() {
                                if audio_extensions.contains(&ext_str.to_lowercase().as_str()) {
                                    let title = path
                                        .file_stem()
                                        .and_then(|s| s.to_str())
                                        .unwrap_or("Unknown")
                                        .to_string();
                                    
                                    let artist = path
                                        .parent()
                                        .and_then(|p| p.file_name())
                                        .and_then(|n| n.to_str())
                                        .unwrap_or("Unknown")
                                        .to_string();
                                    
                                    self.tracks.push(Track {
                                        id: track_id,
                                        title,
                                        artist,
                                        path: path.to_path_buf(),
                                        duration: "0:00".to_string(),
                                    });
                                    
                                    track_id += 1;
                                }
                            }
                        }
                    }
                }
            }
        }
        
        println!("{} {}", "Found".bright_green(), format!("{} audio files", self.tracks.len()).bright_white());
        Ok(())
    }

    fn play_track(&mut self, index: usize) -> Result<()> {
        if index >= self.tracks.len() {
            return Ok(());
        }

        // Stop current playback
        self.stop();

        let track = &self.tracks[index];
        
        // Initialize audio output
        let (stream, stream_handle) = OutputStream::try_default()?;
        let sink = Sink::try_new(&stream_handle)?;
        
        // Load and play the file
        let file = File::open(&track.path)?;
        let source = Decoder::new(BufReader::new(file))?;
        
        sink.set_volume(self.volume);
        sink.append(source);
        
        self._stream = Some(stream);
        self.sink = Some(sink);
        self.current_track = Some(index);
        self.is_playing = true;
        
        println!("{} {}", "♪ Playing:".bright_cyan(), track.title.bright_white());
        
        Ok(())
    }

    fn pause(&mut self) {
        if let Some(sink) = &self.sink {
            if self.is_playing {
                sink.pause();
                self.is_playing = false;
                println!("{}", "⏸ Paused".bright_yellow());
            } else {
                sink.play();
                self.is_playing = true;
                println!("{}", "▶ Resumed".bright_green());
            }
        }
    }

    fn stop(&mut self) {
        if let Some(sink) = &self.sink {
            sink.stop();
        }
        self.sink = None;
        self._stream = None;
        self.is_playing = false;
        self.current_track = None;
        println!("{}", "⏹ Stopped".bright_red());
    }

    fn next_track(&mut self) -> Result<()> {
        if !self.tracks.is_empty() {
            self.selected_index = (self.selected_index + 1) % self.tracks.len();
            self.play_track(self.selected_index)?;
        }
        Ok(())
    }

    fn prev_track(&mut self) -> Result<()> {
        if !self.tracks.is_empty() {
            self.selected_index = if self.selected_index == 0 {
                self.tracks.len() - 1
            } else {
                self.selected_index - 1
            };
            self.play_track(self.selected_index)?;
        }
        Ok(())
    }

    fn set_volume(&mut self, volume: f32) {
        self.volume = volume.clamp(0.0, 1.0);
        if let Some(sink) = &self.sink {
            sink.set_volume(self.volume);
        }
        println!("{} {}%", "🔊 Volume:".bright_blue(), (self.volume * 100.0) as u8);
    }
}

fn main() -> Result<()> {
    // Print header with your color palette
    println!("{}", "═".repeat(60).truecolor(229, 90, 79)); // Coral
    println!("{}", "  ECHO - Terminal Music Player v1.0.0".truecolor(255, 159, 90)); // Peach
    println!("{}", "═".repeat(60).truecolor(229, 90, 79)); // Coral
    println!();

    let mut player = MusicPlayer::new();
    
    // Scan for audio files
    player.scan_audio_files()?;
    
    if player.tracks.is_empty() {
        println!("{}", "No audio files found!".bright_red());
        println!("{}", "Make sure you have MP3, FLAC, WAV, OGG files in:".bright_yellow());
        println!("  • Music folder");
        println!("  • Current directory");
        println!("  • Desktop/Music");
        return Ok(());
    }

    // Show help
    show_help();
    
    // Main loop
    loop {
        // Show current status
        show_status(&player);
        
        // Show track list
        show_tracks(&player);
        
        print!("{} ", "$".truecolor(255, 159, 90)); // Peach prompt
        io::stdout().flush()?;
        
        // Read input
        let mut input = String::new();
        io::stdin().read_line(&mut input)?;
        let input = input.trim();
        
        match input {
            "help" | "h" => show_help(),
            "list" | "l" => {}, // Already showing
            "play" | "p" => {
                if !player.tracks.is_empty() {
                    player.play_track(player.selected_index)?;
                }
            },
            "pause" | " " => player.pause(),
            "stop" | "s" => player.stop(),
            "next" | "n" => player.next_track()?,
            "prev" | "b" => player.prev_track()?,
            "quit" | "q" | "exit" => break,
            cmd if cmd.starts_with("vol ") => {
                if let Ok(vol) = cmd[4..].parse::<f32>() {
                    player.set_volume(vol / 100.0);
                }
            },
            cmd if cmd.starts_with("play ") => {
                if let Ok(index) = cmd[5..].parse::<usize>() {
                    if index > 0 && index <= player.tracks.len() {
                        player.selected_index = index - 1;
                        player.play_track(player.selected_index)?;
                    }
                }
            },
            "clear" | "c" => {
                print!("\x1B[2J\x1B[1;1H"); // Clear screen
            },
            _ => {
                if !input.is_empty() {
                    println!("{} {}", "Unknown command:".bright_red(), input);
                }
            }
        }
        
        println!();
    }
    
    println!("{}", "Goodbye! 🎵".bright_cyan());
    Ok(())
}

fn show_help() {
    println!("{}", "Commands:".truecolor(212, 165, 116)); // Muted brown
    println!("  {} - Show this help", "help".truecolor(255, 159, 90));
    println!("  {} - Play selected track", "play".truecolor(255, 159, 90));
    println!("  {} - Play track by number", "play <num>".truecolor(255, 159, 90));
    println!("  {} - Pause/resume", "pause".truecolor(255, 159, 90));
    println!("  {} - Stop playback", "stop".truecolor(255, 159, 90));
    println!("  {} - Next track", "next".truecolor(255, 159, 90));
    println!("  {} - Previous track", "prev".truecolor(255, 159, 90));
    println!("  {} - Set volume (0-100)", "vol <num>".truecolor(255, 159, 90));
    println!("  {} - Clear screen", "clear".truecolor(255, 159, 90));
    println!("  {} - Quit", "quit".truecolor(255, 159, 90));
    println!();
}

fn show_status(player: &MusicPlayer) {
    if let Some(current) = player.current_track {
        let track = &player.tracks[current];
        let status = if player.is_playing { "▶" } else { "⏸" };
        println!("{} {} - {} | Vol: {}%",
            status.truecolor(229, 90, 79), // Coral
            track.title.truecolor(245, 240, 232), // Cream
            track.artist.truecolor(212, 165, 116), // Muted
            (player.volume * 100.0) as u8
        );
    }
}

fn show_tracks(player: &MusicPlayer) {
    println!("{}", "Audio Files:".truecolor(212, 165, 116)); // Muted brown
    for (i, track) in player.tracks.iter().enumerate().take(10) {
        let prefix = if i == player.selected_index { ">" } else { " " };
        let status = if Some(i) == player.current_track {
            if player.is_playing { "▶" } else { "⏸" }
        } else { " " };

        println!("{}{} {}. {} - {}",
            prefix.truecolor(245, 240, 232), // Cream
            status.truecolor(229, 90, 79), // Coral
            (i + 1).to_string().truecolor(255, 159, 90), // Peach
            track.title.truecolor(245, 240, 232), // Cream
            track.artist.truecolor(212, 165, 116) // Muted
        );
    }

    if player.tracks.len() > 10 {
        println!("  ... and {} more", player.tracks.len() - 10);
    }
    println!();
}


