@import"https://fonts.googleapis.com/css2?family=JetBrains+Mono:ital,wght@0,100..800;1,100..800&display=swap";/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */@layer properties{@supports ((-webkit-hyphens:none) and (not (margin-trim:inline))) or ((-moz-orient:inline) and (not (color:rgb(from red r g b)))){*,:before,:after,::backdrop{--tw-rotate-x:initial;--tw-rotate-y:initial;--tw-rotate-z:initial;--tw-skew-x:initial;--tw-skew-y:initial;--tw-border-style:solid}}}.visible{visibility:visible}.flex{display:flex}.hidden{display:none}.h-full{height:100%}.w-full{width:100%}.flex-1{flex:1}.transform{transform:var(--tw-rotate-x,)var(--tw-rotate-y,)var(--tw-rotate-z,)var(--tw-skew-x,)var(--tw-skew-y,)}.resize{resize:both}.flex-col{flex-direction:column}.items-center{align-items:center}.overflow-y-auto{overflow-y:auto}.border{border-style:var(--tw-border-style);border-width:1px}.border-b{border-bottom-style:var(--tw-border-style);border-bottom-width:1px}.border-none{--tw-border-style:none;border-style:none}.bg-transparent{background-color:#0000}.opacity-0{opacity:0}.opacity-100{opacity:1}.transition-opacity{transition-property:opacity;transition-timing-function:var(--tw-ease,ease);transition-duration:var(--tw-duration,0s)}.outline-none{--tw-outline-style:none;outline-style:none}:root{color-scheme:dark;color:#f5f0e8;font-synthesis:none;text-rendering:optimizeSpeed;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;--echo-cream:#f5f0e8;--echo-peach:#ff9f5a;--echo-coral:#e85a4f;--echo-burgundy:#4a1e1a;--echo-bg:#4a1e1a;--echo-surface:#e85a4f;--echo-accent:#ff9f5a;--echo-text:#f5f0e8;--echo-muted:#d4a574;--echo-border:#8b3a32;will-change:transform;backface-visibility:hidden;perspective:1000px;background-color:#4a1e1a;font-family:JetBrains Mono,Fira Code,Monaco,Consolas,monospace;font-weight:400;line-height:1.4;transform:translateZ(0)}*{box-sizing:border-box}body{background:linear-gradient(135deg,#4a1e1a,#3a1614);min-width:100vw;min-height:100vh;margin:0;padding:0;font-family:JetBrains Mono,monospace;overflow:hidden}#root{flex-direction:column;width:100vw;height:100vh;padding:8px;display:flex}::-webkit-scrollbar{display:none}*{scrollbar-width:none;-ms-overflow-style:none}.cursor{background-color:#ff9f5a;animation:1s infinite blink;display:inline-block}.gpu-accelerated{will-change:transform;transform:translateZ(0)}.smooth-transition{transition:all .2s cubic-bezier(.4,0,.2,1)}.echo-glow{box-shadow:0 0 15px #ff9f5a4d}.echo-glow:hover{box-shadow:0 0 25px #ff9f5a80}.glass{-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);background:#4a1e1acc;border:1px solid #ff9f5a33}@property --tw-rotate-x{syntax:"*";inherits:false}@property --tw-rotate-y{syntax:"*";inherits:false}@property --tw-rotate-z{syntax:"*";inherits:false}@property --tw-skew-x{syntax:"*";inherits:false}@property --tw-skew-y{syntax:"*";inherits:false}@property --tw-border-style{syntax:"*";inherits:false;initial-value:solid}#root{max-width:1280px;margin:0 auto;padding:2rem;text-align:center}.logo{height:6em;padding:1.5em;will-change:filter;transition:filter .3s}.logo:hover{filter:drop-shadow(0 0 2em #646cffaa)}.logo.react:hover{filter:drop-shadow(0 0 2em #61dafbaa)}@keyframes logo-spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}@media (prefers-reduced-motion: no-preference){a:nth-of-type(2) .logo{animation:logo-spin infinite 20s linear}}.card{padding:2em}.read-the-docs{color:#888}
