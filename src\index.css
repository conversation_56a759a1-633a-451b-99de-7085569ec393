@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:ital,wght@0,100..800;1,100..800&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', monospace;
  line-height: 1.4;
  font-weight: 400;

  color-scheme: dark;
  color: #F5F0E8; /* Cream text */
  background-color: #4A1E1A; /* Burgundy background */

  font-synthesis: none;
  text-rendering: optimizeSpeed;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* Echo color palette */
  --echo-cream: #F5F0E8;
  --echo-peach: #FF9F5A;
  --echo-coral: #E85A4F;
  --echo-burgundy: #4A1E1A;
  --echo-bg: #4A1E1A;
  --echo-surface: #E85A4F;
  --echo-accent: #FF9F5A;
  --echo-text: #F5F0E8;
  --echo-muted: #D4A574;
  --echo-border: #8B3A32;

  /* High performance settings for 120fps */
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  min-width: 100vw;
  min-height: 100vh;
  background: linear-gradient(135deg, #4A1E1A 0%, #3A1614 100%);
  overflow: hidden;
  font-family: 'JetBrains Mono', monospace;
}

#root {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 8px;
}

/* Hide scrollbars completely for terminal feel */
::-webkit-scrollbar {
  display: none;
}

* {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

/* Echo cursor */
.cursor {
  display: inline-block;
  background-color: #FF9F5A;
  animation: blink 1s infinite;
}

/* Performance optimizations */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

/* Smooth animations */
.smooth-transition {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Echo glow effects */
.echo-glow {
  box-shadow: 0 0 15px rgba(255, 159, 90, 0.3);
}

.echo-glow:hover {
  box-shadow: 0 0 25px rgba(255, 159, 90, 0.5);
}

/* Glass morphism effect */
.glass {
  backdrop-filter: blur(10px);
  background: rgba(74, 30, 26, 0.8);
  border: 1px solid rgba(255, 159, 90, 0.2);
}
