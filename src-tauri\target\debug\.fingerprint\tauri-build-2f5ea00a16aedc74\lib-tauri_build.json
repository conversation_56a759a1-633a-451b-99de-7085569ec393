{"rustc": 10895048813736897673, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 13412502772462131140, "deps": [[2671782512663819132, "tauri_utils", false, 4392668422150800441], [4899080583175475170, "semver", false, 226480500875875536], [6913375703034175521, "schemars", false, 1282592435485428747], [7170110829644101142, "json_patch", false, 17354958663088037870], [9689903380558560274, "serde", false, 6070878912102744926], [12714016054753183456, "tauri_winres", false, 11544597916428805884], [13077543566650298139, "heck", false, 7464608437653490905], [13475171727366188400, "cargo_toml", false, 15407564825291989461], [13625485746686963219, "anyhow", false, 1292039079741882281], [15367738274754116744, "serde_json", false, 10067859309686628149], [15609422047640926750, "toml", false, 1631193712541391376], [15622660310229662834, "walkdir", false, 1189006774359693792], [16928111194414003569, "dirs", false, 1663566858410743479], [17155886227862585100, "glob", false, 13335192901601259105]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-2f5ea00a16aedc74\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}