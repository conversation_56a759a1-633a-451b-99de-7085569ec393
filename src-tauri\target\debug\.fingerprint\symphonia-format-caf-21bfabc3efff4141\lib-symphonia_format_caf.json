{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[]", "target": 16367991803182316277, "profile": 15657897354478470176, "path": 2739609679416830594, "deps": [[1218881066841546592, "symphonia_core", false, 2022247732038399031], [5986029879202738730, "log", false, 16174589429375859912], [7059103048047618386, "symphonia_metadata", false, 4010796245558092466]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\symphonia-format-caf-21bfabc3efff4141\\dep-lib-symphonia_format_caf", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}