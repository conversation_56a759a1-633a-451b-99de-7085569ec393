import{r as p}from"./vendor-1zw1pNgy.js";var n={exports:{}},e={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var o;function E(){if(o)return e;o=1;var x=Symbol.for("react.transitional.element"),R=Symbol.for("react.fragment");function i(v,r,t){var s=null;if(t!==void 0&&(s=""+t),r.key!==void 0&&(s=""+r.key),"key"in r){t={};for(var u in r)u!=="key"&&(t[u]=r[u])}else t=r;return r=t.ref,{$$typeof:x,type:v,key:s,ref:r!==void 0?r:null,props:t}}return e.Fragment=R,e.jsx=i,e.jsxs=i,e}var a;function d(){return a||(a=1,n.exports=E()),n.exports}var _=d(),f=p();export{_ as j,f as r};
